{"cells": [{"cell_type": "code", "execution_count": 1, "id": "589e40c0", "metadata": {}, "outputs": [], "source": ["from src.common.config.base_settings import BaseAgentSettings"]}, {"cell_type": "code", "execution_count": 2, "id": "b985c397", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GROQ_API_KEY='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' GROQ_LLM_MODEL='llama-3.3-70b-versatile' GROQ_LLM_MODEL_CONTEXT_SUMMARY='llama-3.1-8b-instant' OPENAI_API_KEY='********************************************************************************************************************************************************************' MONGO_URI='mongodb+srv://harsimran1869:<EMAIL>/' MONGO_DB_NAME='ai_agents' COMET_API_KEY='eFGKeDFJvQMkXyfKbc9npaoDR' COMET_PROJECT='ai_agents_platform' TOTAL_MESSAGES_SUMMARY_TRIGGER=30 TOTAL_MESSAGES_AFTER_SUMMARY=5 RAG_TEXT_EMBEDDING_MODEL_ID='sentence-transformers/all-MiniLM-L6-v2' RAG_TEXT_EMBEDDING_MODEL_DIM=384 RAG_TOP_K=3 RAG_DEVICE='cpu' RAG_CHUNK_SIZE=256\n"]}], "source": ["settings = BaseAgentSettings()\n", "print(settings)"]}, {"cell_type": "code", "execution_count": 7, "id": "90b30eb1", "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 8, "id": "cd8abfa7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["--- Executing Fetch Node ---\n", "--- (Fetching HTML from: https://www.google.com/about/careers/applications/jobs/results/110690555461018310-software-engineer-iii-infrastructure-core) ---\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Max input tokens for model groq/llama-3.1-8b-instant not found,\n", "                    please specify the model_tokens parameter in the llm section of the graph configuration.\n", "                    Using default token size: 8192\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Attempt 1 failed: BrowserType.launch: \n", "╔══════════════════════════════════════════════════════╗\n", "║ Host system is missing dependencies to run browsers. ║\n", "║ Please install them with the following command:      ║\n", "║                                                      ║\n", "║     playwright install-deps                          ║\n", "║                                                      ║\n", "║ Alternatively, use apt:                              ║\n", "║     apt-get install libnss3\\                         ║\n", "║         libnspr4\\                                    ║\n", "║         libasound2                                   ║\n", "║                                                      ║\n", "║ <3 Playwright Team                                   ║\n", "╚══════════════════════════════════════════════════════╝\n", "/usr/lib/python3.10/ast.py:276: RuntimeWarning: coroutine 'ChromiumLoader.ascrape_playwright' was never awaited\n", "  for item in field:\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}, {"ename": "RuntimeError", "evalue": "Failed to scrape after 1 attempts: BrowserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Please install them with the following command:      ║\n║                                                      ║\n║     playwright install-deps                          ║\n║                                                      ║\n║ Alternatively, use apt:                              ║\n║     apt-get install libnss3\\                         ║\n║         libnspr4\\                                    ║\n║         libasound2                                   ║\n║                                                      ║\n║ <3 Playwright Team                                   ║\n╚══════════════════════════════════════════════════════╝", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/docloaders/chromium.py:349\u001b[0m, in \u001b[0;36mChromiumLoader.ascrape_playwright\u001b[0;34m(self, url, browser_name)\u001b[0m\n\u001b[1;32m    348\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m browser_name \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mchromium\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 349\u001b[0m     browser \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m p\u001b[38;5;241m.\u001b[39mchromium\u001b[38;5;241m.\u001b[39mlaunch(\n\u001b[1;32m    350\u001b[0m         headless\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mheadless,\n\u001b[1;32m    351\u001b[0m         proxy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproxy,\n\u001b[1;32m    352\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbrowser_config,\n\u001b[1;32m    353\u001b[0m     )\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m browser_name \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfirefox\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py:14451\u001b[0m, in \u001b[0;36mBrowserType.launch\u001b[0;34m(self, executable_path, channel, args, ignore_default_args, handle_sigint, handle_sigterm, handle_sighup, timeout, env, headless, devtools, proxy, downloads_path, slow_mo, traces_dir, chromium_sandbox, firefox_user_prefs)\u001b[0m\n\u001b[1;32m  14358\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"BrowserType.launch\u001b[39;00m\n\u001b[1;32m  14359\u001b[0m \n\u001b[1;32m  14360\u001b[0m \u001b[38;5;124;03mReturns the browser instance.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m  14447\u001b[0m \u001b[38;5;124;03mBrowser\u001b[39;00m\n\u001b[1;32m  14448\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m  14450\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m mapping\u001b[38;5;241m.\u001b[39mfrom_impl(\n\u001b[0;32m> 14451\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_impl_obj\u001b[38;5;241m.\u001b[39mlaunch(\n\u001b[1;32m  14452\u001b[0m         executablePath\u001b[38;5;241m=\u001b[39mexecutable_path,\n\u001b[1;32m  14453\u001b[0m         channel\u001b[38;5;241m=\u001b[39mchannel,\n\u001b[1;32m  14454\u001b[0m         args\u001b[38;5;241m=\u001b[39mmapping\u001b[38;5;241m.\u001b[39mto_impl(args),\n\u001b[1;32m  14455\u001b[0m         ignoreDefaultArgs\u001b[38;5;241m=\u001b[39mmapping\u001b[38;5;241m.\u001b[39mto_impl(ignore_default_args),\n\u001b[1;32m  14456\u001b[0m         handleSIGINT\u001b[38;5;241m=\u001b[39mhandle_sigint,\n\u001b[1;32m  14457\u001b[0m         handleSIGTERM\u001b[38;5;241m=\u001b[39mhandle_sigterm,\n\u001b[1;32m  14458\u001b[0m         handleSIGHUP\u001b[38;5;241m=\u001b[39mhandle_sighup,\n\u001b[1;32m  14459\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[1;32m  14460\u001b[0m         env\u001b[38;5;241m=\u001b[39mmapping\u001b[38;5;241m.\u001b[39mto_impl(env),\n\u001b[1;32m  14461\u001b[0m         headless\u001b[38;5;241m=\u001b[39mheadless,\n\u001b[1;32m  14462\u001b[0m         devtools\u001b[38;5;241m=\u001b[39mdevtools,\n\u001b[1;32m  14463\u001b[0m         proxy\u001b[38;5;241m=\u001b[39mproxy,\n\u001b[1;32m  14464\u001b[0m         downloadsPath\u001b[38;5;241m=\u001b[39mdownloads_path,\n\u001b[1;32m  14465\u001b[0m         slowMo\u001b[38;5;241m=\u001b[39mslow_mo,\n\u001b[1;32m  14466\u001b[0m         tracesDir\u001b[38;5;241m=\u001b[39mtraces_dir,\n\u001b[1;32m  14467\u001b[0m         chromiumSandbox\u001b[38;5;241m=\u001b[39mchromium_sandbox,\n\u001b[1;32m  14468\u001b[0m         firefoxUserPrefs\u001b[38;5;241m=\u001b[39mmapping\u001b[38;5;241m.\u001b[39mto_impl(firefox_user_prefs),\n\u001b[1;32m  14469\u001b[0m     )\n\u001b[1;32m  14470\u001b[0m )\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/playwright/_impl/_browser_type.py:97\u001b[0m, in \u001b[0;36mBrowserType.launch\u001b[0;34m(self, executablePath, channel, args, ignoreDefaultArgs, handleSIGINT, handleSIGTERM, handleSIGHUP, timeout, env, headless, devtools, proxy, downloadsPath, slowMo, tracesDir, chromiumSandbox, firefoxUserPrefs)\u001b[0m\n\u001b[1;32m     95\u001b[0m normalize_launch_params(params)\n\u001b[1;32m     96\u001b[0m browser \u001b[38;5;241m=\u001b[39m cast(\n\u001b[0;32m---> 97\u001b[0m     Browser, from_channel(\u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_channel\u001b[38;5;241m.\u001b[39msend(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlaunch\u001b[39m\u001b[38;5;124m\"\u001b[39m, params))\n\u001b[1;32m     98\u001b[0m )\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_did_launch_browser(browser)\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py:61\u001b[0m, in \u001b[0;36mChannel.send\u001b[0;34m(self, method, params)\u001b[0m\n\u001b[1;32m     60\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msend\u001b[39m(\u001b[38;5;28mself\u001b[39m, method: \u001b[38;5;28mstr\u001b[39m, params: Dict \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Any:\n\u001b[0;32m---> 61\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mwrap_api_call(\n\u001b[1;32m     62\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_inner_send(method, params, \u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m),\n\u001b[1;32m     63\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_internal_type,\n\u001b[1;32m     64\u001b[0m     )\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py:528\u001b[0m, in \u001b[0;36mConnection.wrap_api_call\u001b[0;34m(self, cb, is_internal)\u001b[0m\n\u001b[1;32m    527\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n\u001b[0;32m--> 528\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m rewrite_error(error, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mparsed_st[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mapiName\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00<PERSON><PERSON><PERSON>\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    529\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[0;31mError\u001b[0m: BrowserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Please install them with the following command:      ║\n║                                                      ║\n║     playwright install-deps                          ║\n║                                                      ║\n║ Alternatively, use apt:                              ║\n║     apt-get install libnss3\\                         ║\n║         libnspr4\\                                    ║\n║         libasound2                                   ║\n║                                                      ║\n║ <3 Playwright Team                                   ║\n╚══════════════════════════════════════════════════════╝", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 32\u001b[0m\n\u001b[1;32m     25\u001b[0m scraper \u001b[38;5;241m=\u001b[39m SmartScraperGraph(\n\u001b[1;32m     26\u001b[0m     prompt\u001b[38;5;241m=\u001b[39mprompt,\n\u001b[1;32m     27\u001b[0m     source\u001b[38;5;241m=\u001b[39mjob_url,\n\u001b[1;32m     28\u001b[0m     config\u001b[38;5;241m=\u001b[39mgraph_config\n\u001b[1;32m     29\u001b[0m )\n\u001b[1;32m     31\u001b[0m \u001b[38;5;66;03m# Run the scraper\u001b[39;00m\n\u001b[0;32m---> 32\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mscraper\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/graphs/smart_scraper_graph.py:296\u001b[0m, in \u001b[0;36mSmartScraperGraph.run\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    288\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    289\u001b[0m \u001b[38;5;124;03mExecutes the scraping process and returns the answer to the prompt.\u001b[39;00m\n\u001b[1;32m    290\u001b[0m \n\u001b[1;32m    291\u001b[0m \u001b[38;5;124;03mReturns:\u001b[39;00m\n\u001b[1;32m    292\u001b[0m \u001b[38;5;124;03m    str: The answer to the prompt.\u001b[39;00m\n\u001b[1;32m    293\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    295\u001b[0m inputs \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser_prompt\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprompt, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_key: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msource}\n\u001b[0;32m--> 296\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfinal_state, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexecution_info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgraph\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    298\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfinal_state\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manswer\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo answer found.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/graphs/base_graph.py:358\u001b[0m, in \u001b[0;36mBaseGraph.execute\u001b[0;34m(self, initial_state)\u001b[0m\n\u001b[1;32m    356\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m (result[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_state\u001b[39m\u001b[38;5;124m\"\u001b[39m], [])\n\u001b[1;32m    357\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 358\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_standard\u001b[49m\u001b[43m(\u001b[49m\u001b[43minitial_state\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/graphs/base_graph.py:303\u001b[0m, in \u001b[0;36mBaseGraph._execute_standard\u001b[0;34m(self, initial_state)\u001b[0m\n\u001b[1;32m    290\u001b[0m         graph_execution_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m start_time\n\u001b[1;32m    291\u001b[0m         log_graph_execution(\n\u001b[1;32m    292\u001b[0m             graph_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgraph_name,\n\u001b[1;32m    293\u001b[0m             source\u001b[38;5;241m=\u001b[39msource,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    301\u001b[0m             exception\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(e),\n\u001b[1;32m    302\u001b[0m         )\n\u001b[0;32m--> 303\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    305\u001b[0m exec_info\u001b[38;5;241m.\u001b[39mappend(\n\u001b[1;32m    306\u001b[0m     {\n\u001b[1;32m    307\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnode_name\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTOTAL RESULT\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    314\u001b[0m     }\n\u001b[1;32m    315\u001b[0m )\n\u001b[1;32m    317\u001b[0m graph_execution_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m start_time\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/graphs/base_graph.py:276\u001b[0m, in \u001b[0;36mBaseGraph._execute_standard\u001b[0;34m(self, initial_state)\u001b[0m\n\u001b[1;32m    273\u001b[0m     schema \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_schema(current_node)\n\u001b[1;32m    275\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 276\u001b[0m     result, node_exec_time, cb_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_node\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    277\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcurrent_node\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mllm_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mllm_model_name\u001b[49m\n\u001b[1;32m    278\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    279\u001b[0m     total_exec_time \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m node_exec_time\n\u001b[1;32m    281\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m cb_data:\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/graphs/base_graph.py:200\u001b[0m, in \u001b[0;36mBaseGraph._execute_node\u001b[0;34m(self, current_node, state, llm_model, llm_model_name)\u001b[0m\n\u001b[1;32m    195\u001b[0m curr_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m    197\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcallback_manager\u001b[38;5;241m.\u001b[39mexclusive_get_callback(\n\u001b[1;32m    198\u001b[0m     llm_model, llm_model_name\n\u001b[1;32m    199\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m cb:\n\u001b[0;32m--> 200\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mcurrent_node\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    201\u001b[0m     node_exec_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m curr_time\n\u001b[1;32m    203\u001b[0m     cb_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/nodes/fetch_node.py:116\u001b[0m, in \u001b[0;36mFetchNode.execute\u001b[0;34m(self, state)\u001b[0m\n\u001b[1;32m    114\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandle_local_source(state, source)\n\u001b[1;32m    115\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m input_type \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124murl\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 116\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_web_source\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msource\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    117\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    118\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid input type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minput_type\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/nodes/fetch_node.py:334\u001b[0m, in \u001b[0;36mFetchNode.handle_web_source\u001b[0;34m(self, state, source)\u001b[0m\n\u001b[1;32m    327\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    328\u001b[0m     loader \u001b[38;5;241m=\u001b[39m ChromiumLoader(\n\u001b[1;32m    329\u001b[0m         [source],\n\u001b[1;32m    330\u001b[0m         headless\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mheadless,\n\u001b[1;32m    331\u001b[0m         storage_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstorage_state,\n\u001b[1;32m    332\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mloader_kwargs,\n\u001b[1;32m    333\u001b[0m     )\n\u001b[0;32m--> 334\u001b[0m     document \u001b[38;5;241m=\u001b[39m \u001b[43mloader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    336\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m document \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m document[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mpage_content\u001b[38;5;241m.\u001b[39mstrip():\n\u001b[1;32m    337\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    338\u001b[0m \u001b[38;5;250m        \u001b[39m\u001b[38;5;124;03m\"\"\"No HTML body content found in\u001b[39;00m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;124;03m                     the document fetched by ChromiumLoader.\"\"\"\u001b[39;00m\n\u001b[1;32m    340\u001b[0m     )\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/document_loaders/base.py:32\u001b[0m, in \u001b[0;36mBaseLoader.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mload\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[Document]:\n\u001b[1;32m     31\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Load data into Document objects.\"\"\"\u001b[39;00m\n\u001b[0;32m---> 32\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlazy_load\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/docloaders/chromium.py:456\u001b[0m, in \u001b[0;36mChromiumLoader.lazy_load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    449\u001b[0m scraping_fn \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    450\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mascrape_with_js_support\n\u001b[1;32m    451\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequires_js_support\n\u001b[1;32m    452\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mascrape_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbackend\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    453\u001b[0m )\n\u001b[1;32m    455\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m url \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murls:\n\u001b[0;32m--> 456\u001b[0m     html_content \u001b[38;5;241m=\u001b[39m \u001b[43masyncio\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mscraping_fn\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    457\u001b[0m     metadata \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msource\u001b[39m\u001b[38;5;124m\"\u001b[39m: url}\n\u001b[1;32m    458\u001b[0m     \u001b[38;5;28;01myield\u001b[39;00m Document(page_content\u001b[38;5;241m=\u001b[39mhtml_content, metadata\u001b[38;5;241m=\u001b[39mmetadata)\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/nest_asyncio.py:30\u001b[0m, in \u001b[0;36m_patch_asyncio.<locals>.run\u001b[0;34m(main, debug)\u001b[0m\n\u001b[1;32m     28\u001b[0m task \u001b[38;5;241m=\u001b[39m asyncio\u001b[38;5;241m.\u001b[39mensure_future(main)\n\u001b[1;32m     29\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 30\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mloop\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_until_complete\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m     32\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m task\u001b[38;5;241m.\u001b[39mdone():\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/nest_asyncio.py:98\u001b[0m, in \u001b[0;36m_patch_loop.<locals>.run_until_complete\u001b[0;34m(self, future)\u001b[0m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m f\u001b[38;5;241m.\u001b[39mdone():\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m     97\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEvent loop stopped before Future completed.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m---> 98\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/lib/python3.10/asyncio/futures.py:201\u001b[0m, in \u001b[0;36mFuture.result\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    199\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__log_traceback \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m    200\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m--> 201\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\u001b[38;5;241m.\u001b[39mwith_traceback(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception_tb)\n\u001b[1;32m    202\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n", "File \u001b[0;32m/usr/lib/python3.10/asyncio/tasks.py:232\u001b[0m, in \u001b[0;36mTask.__step\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    228\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    229\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    230\u001b[0m         \u001b[38;5;66;03m# We use the `send` method directly, because coroutines\u001b[39;00m\n\u001b[1;32m    231\u001b[0m         \u001b[38;5;66;03m# don't have `__iter__` and `__next__` methods.\u001b[39;00m\n\u001b[0;32m--> 232\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[43mcoro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    233\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    234\u001b[0m         result \u001b[38;5;241m=\u001b[39m coro\u001b[38;5;241m.\u001b[39mthrow(exc)\n", "File \u001b[0;32m/usr/local/lib/python3.10/dist-packages/scrapegraphai/docloaders/chromium.py:378\u001b[0m, in \u001b[0;36mChromiumLoader.ascrape_playwright\u001b[0;34m(self, url, browser_name)\u001b[0m\n\u001b[1;32m    376\u001b[0m logger\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttempt \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattempt\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m failed: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    377\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m attempt \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretry_limit:\n\u001b[0;32m--> 378\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m    379\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to scrape after \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretry_limit\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m attempts: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    380\u001b[0m     )\n", "\u001b[0;31mRuntimeError\u001b[0m: Failed to scrape after 1 attempts: BrowserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Please install them with the following command:      ║\n║                                                      ║\n║     playwright install-deps                          ║\n║                                                      ║\n║ Alternatively, use apt:                              ║\n║     apt-get install libnss3\\                         ║\n║         libnspr4\\                                    ║\n║         libasound2                                   ║\n║                                                      ║\n║ <3 Playwright Team                                   ║\n╚══════════════════════════════════════════════════════╝"]}], "source": ["from scrapegraphai.graphs import SmartScraperGraph\n", "\n", "# Configuration for the scraper\n", "graph_config = {\n", "    \"llm\": {\n", "        \"model\": \"groq/llama-3.1-8b-instant\",  # Groq model\n", "        \"api_key\": settings.GROQ_API_KEY,       # Replace with your Groq API key\n", "        \"temperature\": 0\n", "    },\n", "    \"verbose\": True,\n", "    \"headless\": False  # Set to True to run without opening a browser\n", "}\n", "\n", "# URL of the job posting\n", "job_url = \"https://www.google.com/about/careers/applications/jobs/results/110690555461018310-software-engineer-iii-infrastructure-core\"  # Replace with the actual job URL\n", "\n", "# Prompt for extracting job description and company description\n", "prompt = \"\"\"\n", "Extract the job description and company description from the provided webpage. \n", "Return a JSON object with two keys: 'job_description' and 'company_description'. \n", "Ensure the extracted text is clean and relevant, excluding unrelated content like navigation menus, footers, or ads.\n", "\"\"\"\n", "\n", "# Initialize the SmartScraperGraph\n", "scraper = SmartScraperGraph(\n", "    prompt=prompt,\n", "    source=job_url,\n", "    config=graph_config\n", ")\n", "\n", "# Run the scraper\n", "result = scraper.run()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "dfdf94ff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "524a68e0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.undefined.undefined"}}, "nbformat": 4, "nbformat_minor": 5}