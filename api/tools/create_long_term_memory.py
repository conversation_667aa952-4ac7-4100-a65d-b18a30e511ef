import json
from pathlib import Path
from typing import List, Dict, Any

import click
from langchain_core.documents import Document

from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryCreator
from career_coaches.config import settings


def create_career_knowledge_documents() -> List[Document]:
    """Create documents from career coaching knowledge base."""
    documents = []

    # Career assessment knowledge
    career_assessment_content = """
    Career Assessment Best Practices:

    1. MBTI Personality Assessment:
    - Use 16personalities.com for free assessment
    - Focus on understanding cognitive functions
    - Connect personality types to suitable career paths
    - Consider work environment preferences

    2. Strong Interest Inventory:
    - Assess interests across six Holland Code areas (RIASEC)
    - Match interests to career clusters
    - Identify potential career satisfaction factors

    3. PRINT Assessment:
    - Uncover unconscious motivators
    - Identify stress triggers in work environments
    - Understand working style preferences

    4. Risk Propensity Assessment:
    - Evaluate comfort with uncertainty
    - Match to industry risk levels
    - Consider entrepreneurial vs. corporate preferences

    5. Values Assessment:
    - Work-life balance priorities
    - Compensation vs. fulfillment trade-offs
    - Company culture preferences
    """

    documents.append(Document(
        page_content=career_assessment_content,
        metadata={
            "source": "career_assessment_knowledge",
            "coach_type": "career_assessment",
            "importance": 1.0
        }
    ))

    # Resume building knowledge
    resume_knowledge_content = """
    Resume Building Best Practices:

    1. ATS Optimization:
    - Use standard section headers
    - Include relevant keywords from job descriptions
    - Use simple formatting without graphics
    - Save in both PDF and Word formats

    2. Content Structure:
    - Start with strong action verbs
    - Quantify achievements with numbers
    - Use STAR method for accomplishments
    - Tailor content to specific job requirements

    3. Common Mistakes to Avoid:
    - Generic objective statements
    - Listing duties instead of achievements
    - Poor formatting and typos
    - Including irrelevant information

    4. Cover Letter Guidelines:
    - Customize for each application
    - Show genuine interest in the company
    - Highlight specific qualifications
    - Keep to one page maximum
    """

    documents.append(Document(
        page_content=resume_knowledge_content,
        metadata={
            "source": "resume_building_knowledge",
            "coach_type": "resume_builder",
            "importance": 1.0
        }
    ))

    # LinkedIn optimization knowledge
    linkedin_knowledge_content = """
    LinkedIn Optimization Best Practices:

    1. Profile Optimization:
    - Professional headline with keywords
    - Compelling about section with achievements
    - Complete work experience with metrics
    - Skills section with endorsements

    2. Content Strategy:
    - Regular posting about industry insights
    - Sharing relevant articles with commentary
    - Engaging with others' content meaningfully
    - Building thought leadership in your field

    3. Networking Approach:
    - Personalized connection requests
    - Following up with meaningful conversations
    - Offering value before asking for help
    - Maintaining relationships consistently

    4. Profile Photo Guidelines:
    - Professional appearance
    - Clear, high-quality image
    - Appropriate background
    - Genuine, approachable expression
    """

    documents.append(Document(
        page_content=linkedin_knowledge_content,
        metadata={
            "source": "linkedin_optimization_knowledge",
            "coach_type": "linkedin_optimizer",
            "importance": 1.0
        }
    ))

    # Networking strategy knowledge
    networking_knowledge_content = """
    Networking Strategy Best Practices:

    1. Networking Mindset:
    - Focus on building genuine relationships
    - Offer value before seeking benefits
    - Be authentic and show genuine interest
    - Follow up consistently and meaningfully

    2. Networking Channels:
    - Professional associations and events
    - Alumni networks and university connections
    - Industry conferences and meetups
    - Online communities and forums

    3. International Student Considerations:
    - Cultural adaptation strategies
    - Building local professional networks
    - Understanding workplace communication norms
    - Leveraging diversity as a strength

    4. Networking Action Plans:
    - Set specific networking goals
    - Track connections and follow-ups
    - Prepare elevator pitches for different contexts
    - Practice active listening skills
    """

    documents.append(Document(
        page_content=networking_knowledge_content,
        metadata={
            "source": "networking_strategy_knowledge",
            "coach_type": "networking_strategy",
            "importance": 1.0
        }
    ))

    return documents


@click.command()
@click.option(
    "--knowledge-only",
    is_flag=True,
    help="Create only general career coaching knowledge base.",
)
@click.option(
    "--user-data-file",
    type=click.Path(exists=True, path_type=Path),
    help="Path to user interaction data JSON file for personalized memory.",
)
def main(knowledge_only: bool, user_data_file: Path = None) -> None:
    """CLI command to create long-term memory for career coaches.

    Args:
        knowledge_only: If True, only create general knowledge base
        user_data_file: Path to user interaction data JSON file
    """
    print("🚀 Creating career coach long-term memory...")

    # Create memory creator
    memory_creator = CareerCoachLongTermMemoryCreator.build_from_settings()

    # Create general knowledge documents
    knowledge_docs = create_career_knowledge_documents()
    print(f"📚 Created {len(knowledge_docs)} knowledge documents")

    # Add knowledge to memory
    memory_creator.create_memory_from_documents(knowledge_docs)
    print("✅ General knowledge base created successfully")

    # If user data file is provided, add user-specific memories
    if not knowledge_only and user_data_file:
        print(f"📊 Processing user data from {user_data_file}")

        with open(user_data_file, 'r') as f:
            user_data = json.load(f)

        for user_entry in user_data.get('users', []):
            user_id = user_entry.get('user_id')
            coach_id = user_entry.get('coach_id', 'career_assessment')

            # Add conversation memories
            for conversation in user_entry.get('conversations', []):
                memory_creator.add_conversation_memory(
                    user_id=user_id,
                    coach_id=coach_id,
                    conversation_summary=conversation.get('summary', ''),
                    key_insights=conversation.get('key_insights', []),
                    goals_discussed=conversation.get('goals_discussed', [])
                )

            # Add progress updates
            for progress in user_entry.get('progress_updates', []):
                memory_creator.update_user_progress(
                    user_id=user_id,
                    coach_id=coach_id,
                    progress_data=progress
                )

            # Add user preferences and goals
            if user_entry.get('preferences'):
                memory_creator.add_user_memory(
                    user_id=user_id,
                    coach_id=coach_id,
                    content=f"User preferences: {json.dumps(user_entry['preferences'], indent=2)}",
                    memory_type="preference",
                    importance=0.9
                )

        print(f"✅ User-specific memories added for {len(user_data.get('users', []))} users")

    print("🎉 Career coach long-term memory creation completed!")


if __name__ == "__main__":
    main()
