from langchain_core.messages import RemoveMessage
from langchain_core.runnables import RunnableConfig
from loguru import logger

from career_coaches.config import settings
from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryRetriever
from .chains import (
    get_context_summary_chain,
    get_conversation_summary_chain,
    get_career_coach_response_chain,
)
from .state import CareerCoachState


async def conversation_node(state: CareerCoachState, config: RunnableConfig):
    """Main conversation node that generates career coach responses with long-term memory context."""
    summary = state.get("summary", "")
    coach_id = state.get("coach_id", "career_assessment")
    user_id = state.get("user_id", "")
    use_web_tools = state.get("use_web_tools", False)
    search_tool_name = state.get("search_tool_name", "all")

    # Get user context from long-term memory
    user_context = ""
    try:
        if user_id:
            memory_retriever = CareerCoachLongTermMemoryRetriever.build_from_settings()

            # Get recent user context
            user_context = memory_retriever.get_recent_user_context(
                user_id=user_id,
                coach_id=coach_id,
                limit=3
            )

            # Also get relevant context based on current conversation
            current_message = state["messages"][-1].content if state["messages"] else ""
            if current_message:
                relevant_memories = memory_retriever.retrieve(
                    query=current_message,
                    user_id=user_id,
                    coach_id=coach_id
                )

                if relevant_memories:
                    memory_context = "\n\nRelevant user history:\n"
                    for memory in relevant_memories[:2]:  # Limit to top 2 relevant memories
                        memory_type = memory.metadata.get("memory_type", "general")
                        memory_context += f"[{memory_type.upper()}] {memory.page_content[:150]}...\n"
                    user_context += memory_context

            logger.info(f"Retrieved user context for {user_id}: {len(user_context)} characters")
    except Exception as e:
        logger.warning(f"Could not retrieve user context for {user_id}: {e}")
        user_context = ""

    # Enhance summary with user context
    enhanced_summary = summary
    if user_context:
        enhanced_summary = f"{summary}\n\nUser Context:\n{user_context}"

    # Get the appropriate chain based on whether web tools are enabled
    if use_web_tools:
        from .tools import configure_web_tools
        # Configure the web tools with the specified search tool name
        web_tools = configure_web_tools(search_tool_name)
        conversation_chain = get_career_coach_response_chain(coach_id, use_web_tools=True)
    else:
        conversation_chain = get_career_coach_response_chain(coach_id, use_web_tools=False)

    response = await conversation_chain.ainvoke(
        {
            "messages": state["messages"],
            "summary": enhanced_summary,
        },
        config,
    )

    return {"messages": response}


async def summarize_conversation_node(state: CareerCoachState):
    """Node that summarizes the conversation when it gets too long."""
    summary = state.get("summary", "")
    summary_chain = get_conversation_summary_chain(summary)

    response = await summary_chain.ainvoke(
        {
            "messages": state["messages"],
            "agent_name": state["coach_name"],  # Using agent_name for compatibility with shared prompts
            "summary": summary,
        }
    )

    # Remove old messages, keeping only the most recent ones
    delete_messages = [
        RemoveMessage(id=m.id)
        for m in state["messages"][: -settings.TOTAL_MESSAGES_AFTER_SUMMARY]
    ]
    return {"summary": response.content, "messages": delete_messages}


async def update_memory_node(state: CareerCoachState):
    """Node to update user's long-term memory with conversation insights."""
    user_id = state.get("user_id", "")
    coach_id = state.get("coach_id", "career_assessment")

    if not user_id:
        logger.warning("No user_id provided, skipping memory update")
        return {}

    try:
        from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryCreator
        memory_creator = CareerCoachLongTermMemoryCreator.build_from_settings()

        # Extract conversation summary from recent messages
        recent_messages = state["messages"][-4:] if len(state["messages"]) >= 4 else state["messages"]

        conversation_text = ""
        key_insights = []
        goals_discussed = []

        for msg in recent_messages:
            if hasattr(msg, 'content'):
                conversation_text += f"{msg.__class__.__name__}: {msg.content}\n"

                # Simple keyword extraction for insights and goals
                content_lower = msg.content.lower()
                if any(word in content_lower for word in ['goal', 'objective', 'want to', 'plan to']):
                    # Extract potential goals
                    sentences = msg.content.split('.')
                    for sentence in sentences:
                        if any(word in sentence.lower() for word in ['goal', 'objective', 'want to', 'plan to']):
                            goals_discussed.append(sentence.strip())

                if any(word in content_lower for word in ['insight', 'realize', 'understand', 'learned']):
                    # Extract potential insights
                    sentences = msg.content.split('.')
                    for sentence in sentences:
                        if any(word in sentence.lower() for word in ['insight', 'realize', 'understand', 'learned']):
                            key_insights.append(sentence.strip())

        # Create conversation summary
        conversation_summary = f"Recent conversation between user {user_id} and coach {coach_id}:\n{conversation_text}"

        # Add conversation memory
        memory_creator.add_conversation_memory(
            user_id=user_id,
            coach_id=coach_id,
            conversation_summary=conversation_summary,
            key_insights=key_insights[:3],  # Limit to top 3
            goals_discussed=goals_discussed[:3]  # Limit to top 3
        )

        logger.info(f"Updated memory for user {user_id} with {len(key_insights)} insights and {len(goals_discussed)} goals")

    except Exception as e:
        logger.error(f"Failed to update user memory: {e}")

    return {}


async def connector_node(state: CareerCoachState):
    """Connector node for workflow transitions."""
    return {}
