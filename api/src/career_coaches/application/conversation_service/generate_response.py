import asyncio
import uuid
import time
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>

from langchain_core.messages import AIMessage, AIMessageChunk, HumanMessage
from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver
from opik.integrations.langchain import OpikTracer
from loguru import logger

from career_coaches.config import settings
from career_coaches.application.history_service import ChatHistoryService
from career_coaches.application.monitoring import monitor
from .workflow.graph import create_career_coach_workflow_graph
from .workflow.state import CareerCoachState


async def get_response(
    messages: str | list[str] | list[dict[str, Any]],
    user_id: str,
    coach_id: str,
    coach_name: str,
    coach_specialty: str,
    coach_approach: str,
    coach_focus_areas: list[str],
    user_context: str = "",
    session_goals: list[str] = None,
    new_thread: bool = False,
    use_web_tools: bool = False,
    search_tool_name: str = "all",
) -> tuple[str, CareerCoachState]:
    """Run a career coaching conversation through the workflow graph.

    Args:
        messages: Initial message to start the conversation.
        user_id: Unique identifier for the user (for multi-user support).
        coach_id: Unique identifier for the career coach.
        coach_name: Name of the career coach.
        coach_specialty: Coach's specialty area.
        coach_approach: Coach's approach and methodology.
        coach_focus_areas: List of coach's focus areas.
        user_context: Additional context about the user's career situation.
        session_goals: Goals for the coaching session.
        new_thread: Whether to create a new conversation thread.

    Returns:
        tuple[str, CareerCoachState]: A tuple containing:
            - The content of the last message in the conversation.
            - The final state after running the workflow.

    Raises:
        RuntimeError: If there's an error running the conversation workflow.
    """
    if session_goals is None:
        session_goals = []

    graph_builder = create_career_coach_workflow_graph()

    # Create thread ID with user_id and coach_id for multi-user support
    thread_id = (
        f"{user_id}_{coach_id}" if not new_thread
        else f"{user_id}_{coach_id}_{uuid.uuid4()}"
    )

    # Start monitoring
    monitor.start_conversation_tracking(user_id, coach_id, thread_id)
    start_time = time.time()

    try:
        async with AsyncMongoDBSaver.from_conn_string(
            conn_string=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            checkpoint_collection_name=settings.MONGO_CAREER_STATE_CHECKPOINT_COLLECTION,
            writes_collection_name=settings.MONGO_CAREER_STATE_WRITES_COLLECTION,
        ) as checkpointer:
            graph = graph_builder.compile(checkpointer=checkpointer)
            opik_tracer = OpikTracer(graph=graph.get_graph(xray=True))

            config = {
                "configurable": {"thread_id": thread_id},
                "callbacks": [opik_tracer],
            }

            # Log user message
            monitor.log_message(thread_id, "user")

            output_state = await graph.ainvoke(
                input={
                    "messages": __format_messages(messages=messages),
                    "user_id": user_id,
                    "coach_id": coach_id,
                    "coach_name": coach_name,
                    "coach_specialty": coach_specialty,
                    "coach_approach": coach_approach,
                    "coach_focus_areas": coach_focus_areas,
                    "user_context": user_context,
                    "session_goals": session_goals,
                    "use_web_tools": use_web_tools,
                    "search_tool_name": search_tool_name,
                },
                config=config,
            )

            # Log coach response
            response_time = time.time() - start_time
            monitor.log_message(thread_id, "coach", response_time)
        last_message = output_state["messages"][-1]
        
        # Save conversation history to Supabase
        formatted_messages = [{
            "role": "user" if isinstance(msg, HumanMessage) else "assistant",
            "content": msg.content
        } for msg in output_state["messages"]]
        
        # Generate a session title from the first few messages
        session_title = None
        if len(formatted_messages) >= 2:  # At least one user message and one assistant response
            first_user_msg = formatted_messages[0]["content"][:30]
            session_title = f"{first_user_msg}..." if len(first_user_msg) >= 30 else first_user_msg
        
        # Save to Supabase asynchronously (don't wait for completion)
        asyncio.create_task(ChatHistoryService.save_chat_session(
            user_id=user_id,
            coach_id=coach_id,
            chat_session_id=thread_id,
            messages=formatted_messages,
            title=session_title
        ))
        
        # End monitoring
        monitor.end_conversation_tracking(thread_id)

        return last_message.content, CareerCoachState(**output_state)
    except Exception as e:
        # Log error and end monitoring
        monitor.log_error(thread_id, str(e))
        monitor.end_conversation_tracking(thread_id)
        raise RuntimeError(f"Error running career coach conversation workflow: {str(e)}") from e


async def get_streaming_response(
    messages: str | list[str] | list[dict[str, Any]],
    user_id: str,
    coach_id: str,
    coach_name: str,
    coach_specialty: str,
    coach_approach: str,
    coach_focus_areas: list[str],
    user_context: str = "",
    session_goals: list[str] = None,
    new_thread: bool = False,
    use_web_tools: bool = False,
    search_tool_name: str = "all",
) -> AsyncGenerator[str, None]:
    """Run a career coaching conversation with streaming response.

    Args:
        messages: Initial message to start the conversation.
        user_id: Unique identifier for the user.
        coach_id: Unique identifier for the career coach.
        coach_name: Name of the career coach.
        coach_specialty: Coach's specialty area.
        coach_approach: Coach's approach and methodology.
        coach_focus_areas: List of coach's focus areas.
        user_context: Additional context about the user's career situation.
        session_goals: Goals for the coaching session.
        new_thread: Whether to create a new conversation thread.

    Yields:
        Chunks of the response as they become available.

    Raises:
        RuntimeError: If there's an error running the conversation workflow.
    """
    if session_goals is None:
        session_goals = []

    graph_builder = create_career_coach_workflow_graph()

    try:
        async with AsyncMongoDBSaver.from_conn_string(
            conn_string=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            checkpoint_collection_name=settings.MONGO_CAREER_STATE_CHECKPOINT_COLLECTION,
            writes_collection_name=settings.MONGO_CAREER_STATE_WRITES_COLLECTION,
        ) as checkpointer:
            graph = graph_builder.compile(checkpointer=checkpointer)
            opik_tracer = OpikTracer(graph=graph.get_graph(xray=True))

            # Create thread ID with user_id and coach_id for multi-user support
            thread_id = (
                f"{user_id}_{coach_id}" if not new_thread
                else f"{user_id}_{coach_id}_{uuid.uuid4()}"
            )
            config = {
                "configurable": {"thread_id": thread_id},
                "callbacks": [opik_tracer],
            }

            async for chunk in graph.astream(
                input={
                    "messages": __format_messages(messages=messages),
                    "user_id": user_id,
                    "coach_id": coach_id,
                    "coach_name": coach_name,
                    "coach_specialty": coach_specialty,
                    "coach_approach": coach_approach,
                    "coach_focus_areas": coach_focus_areas,
                    "user_context": user_context,
                    "session_goals": session_goals,
                    "use_web_tools": use_web_tools,
                    "search_tool_name": search_tool_name,
                },
                config=config,
                stream_mode="messages",
            ):
                if chunk[1]["langgraph_node"] == "conversation_node" and isinstance(
                    chunk[0], AIMessageChunk
                ):
                    yield chunk[0].content
                    
                    # We can potentially save the streaming chunks to Supabase, but
                    # it's more efficient to wait until the full response is complete

    except Exception as e:
        raise RuntimeError(
            f"Error running streaming career coach conversation workflow: {str(e)}"
        ) from e


def __format_messages(
    messages: Union[str, list[dict[str, Any]]],
) -> list[Union[HumanMessage, AIMessage]]:
    """Convert various message formats to a list of LangChain message objects.

    Args:
        messages: Can be one of:
            - A single string message
            - A list of string messages
            - A list of dictionaries with 'role' and 'content' keys

    Returns:
        List[Union[HumanMessage, AIMessage]]: A list of LangChain message objects
    """

    if isinstance(messages, str):
        return [HumanMessage(content=messages)]

    if isinstance(messages, list):
        if not messages:
            return []

        if (
            isinstance(messages[0], dict)
            and "role" in messages[0]
            and "content" in messages[0]
        ):
            result = []
            for msg in messages:
                if msg["role"] == "user":
                    result.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    result.append(AIMessage(content=msg["content"]))
            return result

        return [HumanMessage(content=message) for message in messages]

    return []
