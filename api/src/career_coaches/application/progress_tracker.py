"""
User progress tracking system for career coaching.
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from loguru import logger
from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryCreator, CareerCoachLongTermMemoryRetriever
from career_coaches.application.history_service import ChatHistoryService


class ProgressStatus(Enum):
    """Progress status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"


@dataclass
class Goal:
    """Represents a user's career goal."""
    id: str
    title: str
    description: str
    target_date: str
    status: ProgressStatus
    priority: int  # 1-5, where 5 is highest priority
    category: str  # career_change, skill_development, job_search, networking, etc.
    created_at: str
    updated_at: str
    completion_percentage: int = 0
    milestones: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.milestones is None:
            self.milestones = []


@dataclass
class Achievement:
    """Represents a user's achievement."""
    id: str
    title: str
    description: str
    date_achieved: str
    category: str
    impact_level: int  # 1-5, where 5 is highest impact
    evidence: str = ""  # Links, certificates, etc.
    skills_gained: List[str] = None
    
    def __post_init__(self):
        if self.skills_gained is None:
            self.skills_gained = []


@dataclass
class SkillAssessment:
    """Represents a user's skill assessment."""
    skill_name: str
    current_level: int  # 1-5 scale
    target_level: int  # 1-5 scale
    last_assessed: str
    learning_resources: List[str] = None
    
    def __post_init__(self):
        if self.learning_resources is None:
            self.learning_resources = []


class UserProgressTracker:
    """Tracks and manages user progress in career development."""
    
    def __init__(self):
        self.memory_creator = CareerCoachLongTermMemoryCreator.build_from_settings()
        self.memory_retriever = CareerCoachLongTermMemoryRetriever.build_from_settings()
    
    def add_goal(
        self,
        user_id: str,
        coach_id: str,
        title: str,
        description: str,
        target_date: str,
        category: str,
        priority: int = 3
    ) -> Goal:
        """Add a new goal for the user."""
        goal_id = f"goal_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        goal = Goal(
            id=goal_id,
            title=title,
            description=description,
            target_date=target_date,
            status=ProgressStatus.NOT_STARTED,
            priority=priority,
            category=category,
            created_at=datetime.utcnow().isoformat(),
            updated_at=datetime.utcnow().isoformat()
        )
        
        # Store in long-term memory
        goal_content = f"""
        New Goal Set:
        Title: {title}
        Description: {description}
        Category: {category}
        Priority: {priority}/5
        Target Date: {target_date}
        Status: {goal.status.value}
        """
        
        self.memory_creator.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=goal_content,
            memory_type="goal",
            importance=0.9,
            metadata={
                "goal_id": goal_id,
                "goal_data": asdict(goal)
            }
        )
        
        logger.info(f"Added goal '{title}' for user {user_id}")
        return goal
    
    def update_goal_progress(
        self,
        user_id: str,
        coach_id: str,
        goal_id: str,
        status: ProgressStatus = None,
        completion_percentage: int = None,
        milestone: Dict[str, Any] = None
    ) -> None:
        """Update progress on a specific goal."""
        update_content = f"""
        Goal Progress Update:
        Goal ID: {goal_id}
        Date: {datetime.utcnow().isoformat()}
        """
        
        if status:
            update_content += f"\nStatus Updated: {status.value}"
        
        if completion_percentage is not None:
            update_content += f"\nCompletion: {completion_percentage}%"
        
        if milestone:
            update_content += f"\nMilestone Achieved: {milestone.get('title', 'Unnamed milestone')}"
            update_content += f"\nMilestone Description: {milestone.get('description', '')}"
        
        self.memory_creator.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=update_content,
            memory_type="progress",
            importance=0.8,
            metadata={
                "goal_id": goal_id,
                "status": status.value if status else None,
                "completion_percentage": completion_percentage,
                "milestone": milestone
            }
        )
        
        logger.info(f"Updated goal progress for goal {goal_id}, user {user_id}")
    
    def add_achievement(
        self,
        user_id: str,
        coach_id: str,
        title: str,
        description: str,
        category: str,
        impact_level: int = 3,
        evidence: str = "",
        skills_gained: List[str] = None
    ) -> Achievement:
        """Record a new achievement for the user."""
        achievement_id = f"achievement_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        achievement = Achievement(
            id=achievement_id,
            title=title,
            description=description,
            date_achieved=datetime.utcnow().isoformat(),
            category=category,
            impact_level=impact_level,
            evidence=evidence,
            skills_gained=skills_gained or []
        )
        
        # Store in long-term memory
        achievement_content = f"""
        New Achievement Unlocked:
        Title: {title}
        Description: {description}
        Category: {category}
        Impact Level: {impact_level}/5
        Date: {achievement.date_achieved}
        Skills Gained: {', '.join(achievement.skills_gained)}
        Evidence: {evidence}
        """
        
        self.memory_creator.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=achievement_content,
            memory_type="achievement",
            importance=0.9,
            metadata={
                "achievement_id": achievement_id,
                "achievement_data": asdict(achievement)
            }
        )
        
        logger.info(f"Added achievement '{title}' for user {user_id}")
        return achievement
    
    def update_skill_assessment(
        self,
        user_id: str,
        coach_id: str,
        skill_name: str,
        current_level: int,
        target_level: int = None,
        learning_resources: List[str] = None
    ) -> SkillAssessment:
        """Update or create a skill assessment."""
        skill_assessment = SkillAssessment(
            skill_name=skill_name,
            current_level=current_level,
            target_level=target_level or current_level + 1,
            last_assessed=datetime.utcnow().isoformat(),
            learning_resources=learning_resources or []
        )
        
        # Store in long-term memory
        skill_content = f"""
        Skill Assessment Update:
        Skill: {skill_name}
        Current Level: {current_level}/5
        Target Level: {skill_assessment.target_level}/5
        Gap: {skill_assessment.target_level - current_level} levels
        Last Assessed: {skill_assessment.last_assessed}
        Learning Resources: {', '.join(skill_assessment.learning_resources)}
        """
        
        self.memory_creator.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=skill_content,
            memory_type="skill_assessment",
            importance=0.7,
            metadata={
                "skill_name": skill_name,
                "skill_data": asdict(skill_assessment)
            }
        )
        
        logger.info(f"Updated skill assessment for '{skill_name}' for user {user_id}")
        return skill_assessment
    
    def get_user_progress_summary(self, user_id: str, coach_id: str = None) -> Dict[str, Any]:
        """Get a comprehensive progress summary for the user."""
        try:
            # Get memory summary
            memory_summary = self.memory_retriever.get_user_memory_summary(user_id, coach_id)
            
            # Get recent progress updates
            progress_docs = self.memory_retriever.retrieve(
                query="progress goal achievement",
                user_id=user_id,
                coach_id=coach_id,
                memory_type="progress"
            )
            
            # Get achievements
            achievement_docs = self.memory_retriever.retrieve(
                query="achievement",
                user_id=user_id,
                coach_id=coach_id,
                memory_type="achievement"
            )
            
            # Get goals
            goal_docs = self.memory_retriever.retrieve(
                query="goal",
                user_id=user_id,
                coach_id=coach_id,
                memory_type="goal"
            )
            
            return {
                "user_id": user_id,
                "coach_id": coach_id,
                "summary_date": datetime.utcnow().isoformat(),
                "memory_summary": memory_summary,
                "total_goals": len(goal_docs),
                "total_achievements": len(achievement_docs),
                "recent_progress_updates": len(progress_docs),
                "goals": [doc.page_content for doc in goal_docs[:5]],
                "achievements": [doc.page_content for doc in achievement_docs[:5]],
                "recent_progress": [doc.page_content for doc in progress_docs[:3]]
            }
            
        except Exception as e:
            logger.error(f"Error getting progress summary for user {user_id}: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "summary_date": datetime.utcnow().isoformat()
            }
