import asyncio
from typing import Dict, Any, List

import opik
from loguru import logger
from opik.evaluation import evaluate
from opik.evaluation.metrics import (
    AnswerRelevance,
    Hallucination,
    Moderation,
)

from career_coaches.application.conversation_service.generate_response import get_response
from career_coaches.application.conversation_service.workflow import state_to_str
from career_coaches.application.evaluation.custom_metrics import get_career_coaching_metrics
from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryRetriever
from career_coaches.config import settings
from career_coaches.domain.coach_factory import CoachFactory


async def career_coach_evaluation_task(x: dict) -> dict:
    """Calls career coach app logic to evaluate coach responses with enhanced context.

    Args:
        x: Dictionary containing evaluation data with the following keys:
            messages: List of conversation messages where all but the last are inputs
                and the last is the expected output
            coach_id: ID of the career coach to use
            user_id: ID of the user for multi-user support
            use_memory: Whether to include user's long-term memory context

    Returns:
        dict: Dictionary with evaluation results containing:
            input: Original input messages
            context: Context used for generating the response
            output: Generated response from career coach
            expected_output: Expected answer for comparison
            user_context: User's historical context from memory
    """

    coach_factory = CoachFactory()
    coach = coach_factory.get_coach(x["coach_id"])

    input_messages = x["messages"][:-1]
    expected_output_message = x["messages"][-1]
    user_id = x.get("user_id", "test_user")
    use_memory = x.get("use_memory", False)

    # Get user context from long-term memory if requested
    user_context = ""
    if use_memory:
        try:
            memory_retriever = CareerCoachLongTermMemoryRetriever.build_from_settings()
            user_context = memory_retriever.get_recent_user_context(
                user_id=user_id,
                coach_id=coach.id,
                limit=3
            )
        except Exception as e:
            logger.warning(f"Could not retrieve user context: {e}")
            user_context = ""

    response, latest_state = await get_response(
        messages=input_messages,
        user_id=user_id,
        coach_id=coach.id,
        coach_name=coach.name,
        coach_specialty=coach.specialty,
        coach_approach=coach.approach,
        coach_focus_areas=coach.focus_areas,
        user_context=user_context,
        new_thread=True,
    )
    context = state_to_str(latest_state)

    return {
        "input": input_messages,
        "context": context,
        "output": response,
        "expected_output": expected_output_message,
        "user_context": user_context,
    }


def get_used_prompts() -> list[opik.Prompt]:
    """Get the prompts used in career coaching for evaluation tracking."""
    client = opik.Opik()

    prompts = [
        client.get_prompt(name="career_assessment_prompt"),
        client.get_prompt(name="resume_builder_prompt"),
        client.get_prompt(name="linkedin_optimizer_prompt"),
        client.get_prompt(name="networking_strategy_prompt"),
        client.get_prompt(name="summary_prompt"),
        client.get_prompt(name="extend_summary_prompt"),
    ]
    prompts = [p for p in prompts if p is not None]

    return prompts


def create_evaluation_dataset_from_conversations(
    conversations: List[Dict[str, Any]],
    dataset_name: str = "career_coach_evaluation"
) -> opik.Dataset:
    """Create an evaluation dataset from conversation data.

    Args:
        conversations: List of conversation dictionaries
        dataset_name: Name for the dataset

    Returns:
        Created Opik dataset
    """
    client = opik.Opik()

    # Convert conversations to evaluation format
    evaluation_items = []
    for conv in conversations:
        messages = conv.get("messages", [])
        if len(messages) >= 2:  # Need at least user input and coach response
            evaluation_items.append({
                "messages": messages,
                "coach_id": conv.get("coach_id", "career_assessment"),
                "user_id": conv.get("user_id", "test_user"),
                "use_memory": conv.get("use_memory", False)
            })

    # Create dataset
    dataset = client.create_dataset(
        name=dataset_name,
        description="Career coaching evaluation dataset with conversation examples"
    )

    # Add items to dataset
    dataset.insert(evaluation_items)

    logger.info(f"Created evaluation dataset '{dataset_name}' with {len(evaluation_items)} items")
    return dataset


def evaluate_career_coach(
    dataset: opik.Dataset | None,
    workers: int = 2,
    nb_samples: int | None = None,
) -> None:
    """Evaluates a career coach using specified metrics and dataset.

    Runs evaluation using Opik framework with configured metrics for career coaching
    effectiveness including response relevance, actionability, and coaching quality.

    Args:
        dataset: Dataset containing evaluation examples.
            Must contain messages, coach_id, and user_id.
        workers: Number of parallel workers to use for evaluation.
            Defaults to 2.
        nb_samples: Optional number of samples to evaluate.
            If None, evaluates the entire dataset.

    Raises:
        ValueError: If dataset is None
        AssertionError: If COMET_API_KEY is not set

    Returns:
        None
    """

    assert settings.COMET_API_KEY, (
        "COMET_API_KEY is not set. We need it to track the experiment with Opik."
    )

    if not dataset:
        raise ValueError("Dataset is 'None'.")

    logger.info("Starting career coach evaluation...")

    experiment_config = {
        "model_id": settings.GROQ_LLM_MODEL,
        "dataset_name": dataset.name,
        "agent_type": "career_coach",
    }
    used_prompts = get_used_prompts()

    # Career coaching specific metrics - combining standard and custom metrics
    scoring_metrics = [
        AnswerRelevance(),  # How relevant is the coaching advice
        Hallucination(),    # Accuracy of information provided
        Moderation(),       # Appropriate and professional tone
    ]

    # Add custom career coaching metrics
    custom_metrics = get_career_coaching_metrics()
    scoring_metrics.extend(custom_metrics)

    logger.info("Career coach evaluation details:")
    logger.info(f"Dataset: {dataset.name}")
    logger.info(f"Metrics: {[m.__class__.__name__ for m in scoring_metrics]}")

    evaluate(
        dataset=dataset,
        task=lambda x: asyncio.run(career_coach_evaluation_task(x)),
        scoring_metrics=scoring_metrics,
        experiment_config=experiment_config,
        task_threads=workers,
        nb_samples=nb_samples,
        prompts=used_prompts,
    )
