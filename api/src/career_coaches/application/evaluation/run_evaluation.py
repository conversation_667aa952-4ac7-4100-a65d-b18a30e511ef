"""
<PERSON><PERSON><PERSON> to run comprehensive career coach evaluation with custom metrics.
"""

import asyncio
import json
from pathlib import Path
from typing import Dict, Any, List

import click
import opik
from loguru import logger

from career_coaches.application.evaluation.evaluate import (
    evaluate_career_coach,
    create_evaluation_dataset_from_conversations
)
from career_coaches.config import settings


def load_evaluation_conversations(file_path: Path) -> List[Dict[str, Any]]:
    """Load evaluation conversations from JSON file."""
    if not file_path.exists():
        logger.error(f"Evaluation file not found: {file_path}")
        return []
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    return data.get('conversations', [])


def create_sample_evaluation_data() -> List[Dict[str, Any]]:
    """Create sample evaluation conversations for testing."""
    return [
        {
            "messages": [
                {"role": "user", "content": "Hi, I'm a recent computer science graduate and I'm not sure what career path to take. Can you help me?"},
                {"role": "assistant", "content": "Hello! I'm <PERSON>, your AI career coach. I'm excited to help you explore your career options! As a recent CS graduate, you have many exciting paths ahead. To give you the best guidance, I'd love to learn more about you. What aspects of computer science did you enjoy most during your studies?"}
            ],
            "coach_id": "career_assessment",
            "user_id": "eval_user_001",
            "use_memory": False
        },
        {
            "messages": [
                {"role": "user", "content": "I need help optimizing my resume for software engineering positions. I have some projects but I'm not sure how to present them effectively."},
                {"role": "assistant", "content": "I'd be happy to help you optimize your resume for software engineering roles! Projects are fantastic ways to showcase your skills. To give you the most targeted advice, could you share one of your projects and tell me about the technologies you used and what problem it solved?"}
            ],
            "coach_id": "resume_builder",
            "user_id": "eval_user_002",
            "use_memory": False
        },
        {
            "messages": [
                {"role": "user", "content": "My LinkedIn profile feels boring and I'm not getting any recruiter attention. What should I do?"},
                {"role": "assistant", "content": "I understand your frustration! A compelling LinkedIn profile can make a huge difference in attracting recruiters. Let's transform your profile into a recruiter magnet! First, could you tell me what your current headline says? This is often the first thing that needs optimization to grab attention."}
            ],
            "coach_id": "linkedin_optimizer",
            "user_id": "eval_user_003",
            "use_memory": False
        },
        {
            "messages": [
                {"role": "user", "content": "I'm an international student and I find networking really challenging. I don't know how to approach people or what to say."},
                {"role": "assistant", "content": "I completely understand your challenge - networking can feel intimidating, especially as an international student! But you have unique perspectives and experiences that are actually valuable to others. Let's build your confidence step by step. What's your field of study, and are there any specific industries or roles you're targeting?"}
            ],
            "coach_id": "networking_strategy",
            "user_id": "eval_user_004",
            "use_memory": False
        },
        {
            "messages": [
                {"role": "user", "content": "I completed my MBTI assessment and got INTJ. What does this mean for my career choices?"},
                {"role": "assistant", "content": "Excellent! INTJ is a fascinating personality type with some wonderful career strengths. As an INTJ, you're likely a strategic thinker who enjoys working independently and solving complex problems. You probably prefer depth over breadth and value competence highly. Some career paths that often appeal to INTJs include data science, software architecture, research, consulting, and strategic planning roles. What resonates with you from this description, and are there any specific fields you've been considering?"}
            ],
            "coach_id": "career_assessment",
            "user_id": "eval_user_005",
            "use_memory": False
        }
    ]


@click.command()
@click.option(
    "--evaluation-file",
    type=click.Path(path_type=Path),
    help="Path to JSON file containing evaluation conversations",
)
@click.option(
    "--use-sample-data",
    is_flag=True,
    help="Use built-in sample evaluation data",
)
@click.option(
    "--dataset-name",
    default="career_coach_evaluation",
    help="Name for the evaluation dataset",
)
@click.option(
    "--workers",
    default=2,
    help="Number of parallel workers for evaluation",
)
@click.option(
    "--nb-samples",
    type=int,
    help="Number of samples to evaluate (optional)",
)
def main(
    evaluation_file: Path = None,
    use_sample_data: bool = False,
    dataset_name: str = "career_coach_evaluation",
    workers: int = 2,
    nb_samples: int = None
):
    """Run comprehensive career coach evaluation with custom metrics."""
    
    # Check if API key is set
    if not settings.COMET_API_KEY:
        logger.error("COMET_API_KEY is not set. Please set it to run evaluations.")
        return
    
    logger.info("🚀 Starting career coach evaluation...")
    
    # Load evaluation data
    conversations = []
    if use_sample_data:
        logger.info("📊 Using sample evaluation data")
        conversations = create_sample_evaluation_data()
    elif evaluation_file:
        logger.info(f"📁 Loading evaluation data from {evaluation_file}")
        conversations = load_evaluation_conversations(evaluation_file)
    else:
        logger.error("❌ Please specify either --evaluation-file or --use-sample-data")
        return
    
    if not conversations:
        logger.error("❌ No evaluation conversations found")
        return
    
    logger.info(f"📋 Loaded {len(conversations)} evaluation conversations")
    
    # Create evaluation dataset
    try:
        dataset = create_evaluation_dataset_from_conversations(
            conversations=conversations,
            dataset_name=dataset_name
        )
        logger.info(f"✅ Created evaluation dataset: {dataset.name}")
    except Exception as e:
        logger.error(f"❌ Failed to create evaluation dataset: {e}")
        return
    
    # Run evaluation
    try:
        logger.info("🔍 Running evaluation with custom metrics...")
        logger.info(f"   - Workers: {workers}")
        logger.info(f"   - Samples: {nb_samples or 'all'}")
        logger.info("   - Metrics: AnswerRelevance, Hallucination, Moderation, Actionability, Empathy, GoalAlignment, CoachingQuality")
        
        evaluate_career_coach(
            dataset=dataset,
            workers=workers,
            nb_samples=nb_samples
        )
        
        logger.info("🎉 Evaluation completed successfully!")
        logger.info(f"📊 Check your Opik dashboard for detailed results")
        
    except Exception as e:
        logger.error(f"❌ Evaluation failed: {e}")
        raise


if __name__ == "__main__":
    main()
