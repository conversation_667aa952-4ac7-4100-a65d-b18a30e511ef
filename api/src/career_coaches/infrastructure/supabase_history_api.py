from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional

from career_coaches.application.history_service import ChatHistoryService


router = APIRouter()


class ChatSessionResponse(BaseModel):
    id: str
    user_id: str
    coach_id: str
    chat_session_id: str
    title: Optional[str] = None
    created_at: str
    updated_at: str


class ChatSessionDetailResponse(ChatSessionResponse):
    messages: list


class ChatSessionTitleRequest(BaseModel):
    title: str


@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(user_id: str):
    """Get all chat sessions for a user."""
    try:
        sessions = await ChatHistoryService.get_chat_sessions_by_user(user_id)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat sessions: {str(e)}")


@router.get("/sessions/{chat_session_id}", response_model=ChatSessionDetailResponse)
async def get_chat_session(user_id: str, chat_session_id: str):
    """Get a specific chat session with all messages."""
    try:
        session = await ChatHistoryService.get_chat_session(user_id, chat_session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat session: {str(e)}")


@router.delete("/sessions/{chat_session_id}")
async def delete_chat_session(user_id: str, chat_session_id: str):
    """Delete a chat session."""
    try:
        result = await ChatHistoryService.delete_chat_session(user_id, chat_session_id)
        if not result:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return {"success": True, "message": "Chat session deleted"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete chat session: {str(e)}")


@router.patch("/sessions/{chat_session_id}/title")
async def update_chat_session_title(user_id: str, chat_session_id: str, request: ChatSessionTitleRequest):
    """Update the title of a chat session."""
    try:
        result = await ChatHistoryService.update_chat_session_title(
            user_id, chat_session_id, request.title
        )
        if not result:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return {"success": True, "message": "Chat session title updated"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update chat session title: {str(e)}")
