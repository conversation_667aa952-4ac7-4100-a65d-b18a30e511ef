-- Create coach_chat_sessions table to store chat history
CREATE TABLE IF NOT EXISTS coach_chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    coach_id TEXT NOT NULL,
    chat_session_id TEXT NOT NULL,
    title TEXT,
    messages JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Create unique constraint on user_id and chat_session_id
    CONSTRAINT unique_user_chat_session UNIQUE (user_id, chat_session_id)
);

-- Create index for user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_user_id ON coach_chat_sessions(user_id);

-- Create index for coach_id for faster queries
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_coach_id ON coach_chat_sessions(coach_id);

-- Create function to update updated_at column automatically
CREATE OR REPLACE FUNCTION update_coach_chat_sessions_updated_at()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at column automatically
DROP TRIGGER IF EXISTS update_coach_chat_sessions_updated_at_trigger ON coach_chat_sessions;
CREATE TRIGGER update_coach_chat_sessions_updated_at_trigger
BEFORE UPDATE ON coach_chat_sessions
FOR EACH ROW
EXECUTE FUNCTION update_coach_chat_sessions_updated_at();
