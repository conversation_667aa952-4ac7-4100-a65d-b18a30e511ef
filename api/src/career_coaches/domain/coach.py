from typing import List
from pydantic import BaseModel, Field


class Coach(BaseModel):
    """A class representing a career coach agent with specialized expertise.

    Args:
        id (str): Unique identifier for the career coach.
        name (str): Name of the career coach.
        specialty (str): The coach's area of expertise and specialization.
        approach (str): Description of the coach's methodology and coaching style.
        focus_areas (List[str]): Key areas of focus and expertise.
    """

    id: str = Field(description="Unique identifier for the career coach")
    name: str = Field(description="Name of the career coach")
    specialty: str = Field(description="The coach's area of expertise and specialization")
    approach: str = Field(description="Description of the coach's methodology and coaching style")
    focus_areas: List[str] = Field(description="Key areas of focus and expertise")

    def __str__(self) -> str:
        return f"Coach(id={self.id}, name={self.name}, specialty={self.specialty}, approach={self.approach}, focus_areas={self.focus_areas})"
    async def generate_response(self, query: str, chat_session) -> str:
        try:
            # Check if coach has async get_response method
            if hasattr(chat_session.coach, 'get_response'):
                if asyncio.iscoroutinefunction(chat_session.coach.get_response):
                    response = await chat_session.coach.get_response(query, chat_session.conversation_history)
                else:
                    response = chat_session.coach.get_response(query, chat_session.conversation_history)
            else:
                # Fallback response generation
                response = self._generate_fallback_response(query, chat_session)
            
            return response
        except Exception as e:
            print(f"⚠️ Error generating response: {e}")
            return self._generate_fallback_response(query, chat_session)
    
    def _generate_fallback_response(self, query: str, chat_session) -> str:
        query_type = self._classify_query(query)
        
        if query_type == QueryType.SIMPLE_QUESTION:
            if any(greeting in query.lower() for greeting in ['hi', 'hello', 'hey']):
                return "Hello! I'm Sophie, your AI career coach. I'm doing great and I'm excited to help you with your career journey today! What would you like to explore?"
            else:
                return f"Thank you for your question about '{query[:30]}...'. I'm here to help you with your career development. Could you tell me more about what you're looking for?"
        
        elif query_type == QueryType.ADVICE_REQUEST:
            return f"I'd be happy to help you with career advice. Based on your question, let me share some guidance that could be valuable for your situation."
        
        elif query_type == QueryType.COMPLEX_ANALYSIS:
            return f"This is a great question that requires some thoughtful analysis. Let me break this down for you and provide comprehensive guidance."
        
        else:
            return "I'm Sophie, your career coach, and I'm here to support you. How can I help you with your career goals today?"
    