%==== PACKAGES AND OTHER DOCUMENT CONFIGURATIONS  ====%
\documentclass{resume} % Use the custom resume.cls style
\usepackage[left=0.25in,top=0.25in,right=0.25in,bottom=0.25in]{geometry} % Document margins
\usepackage[T1]{fontenc}
\usepackage{xcolor}
\usepackage{lmodern}
\usepackage[T1]{fontenc}
\usepackage{fontawesome} % For GitHub and LinkedIn symbols
\usepackage{textcomp} % For mobile phone and email symbols
% \usepackage[colorlinks=true, linkcolor=blue, citecolor=blue, urlcolor=blue]{hyperref}
\usepackage{xcolor}  % Required for defining custom colors
\usepackage{hyperref}
% Define your custom colors
% \definecolor{myblue}{RGB}{173, 216, 246}
% \definecolor{myblue}{RGB}{123, 176, 206}
\definecolor{myblue}{RGB}{0, 164, 218}

% Set hyperlink colors
\hypersetup{
    colorlinks=true,
    linkcolor=myblue,
    citecolor=myblue,
    urlcolor=myblue
}

\usepackage{hyperref}

%==== Headings ====%
\name{\VAR{personal.name}} % Your name
\address{
\BLOCK{ if personal.phone }{\faPhone} \href{tel:\VAR{personal.phone}}{\VAR{personal.phone.replace(' ','')}} \quad \BLOCK{ endif }
\BLOCK{ if personal.email }{\faEnvelope} \href{mailto:\VAR{personal.email}}{\VAR{personal.email}} \quad \BLOCK{ endif }
\BLOCK{ if personal.github }{\faGithub} \href{\VAR{personal.github}}{\VAR{personal.github}} \quad \BLOCK{ endif }
\BLOCK{ if personal.linkedin }{\faLinkedin} \href{\VAR{personal.linkedin}}{\VAR{personal.linkedin}} \BLOCK{ endif }
}

\begin{document}

%===== WORK EXPERIENCE SECTION =====%
\BLOCK{ if work_experience }
    \begin{rSection}{Work Experience}
        \BLOCK{ for exp in work_experience }
            \begin{rSubsection}
                {\VAR{exp.role}}{\BLOCK{ if exp.from_date }\VAR{exp.from_date} -\BLOCK{ endif } \BLOCK{ if exp.from_date }\VAR{exp.to_date}\BLOCK{ endif }}
                \BLOCK{ if exp.link}
                    {\normalfont{\textit{\href{\VAR{exp.link}}{\VAR{exp.company}}}}}
                \BLOCK{else}
                    {\normalfont{\textit{\VAR{exp.company}}}}
                \BLOCK{ endif }
                {\normalfont{\textit{\VAR{exp.location}}}}
                \BLOCK{ for point in exp.description }
                    \item \VAR{point}
                \BLOCK{ endfor }
            \end{rSubsection}
        \BLOCK{ endfor }
    \end{rSection}
\BLOCK{ endif }

%==== EDUCATION SECTION ====%
\BLOCK{ if education }
\begin{rSection}{Education}
    \BLOCK{ for school in education }
        \BLOCK{ if school.university }
            \textbf{\VAR{school.university}} \hfill {\VAR{school.from_date} - \VAR{school.to_date}} \\
            \BLOCK{ if school.degree }
                {\VAR{school.degree}}
            \BLOCK{ endif }
            \BLOCK{ if school.grade }
                \hfill {(GPA: \VAR{school.grade})}
            \BLOCK{ endif } 
            \BLOCK{ if school.coursework }
                \textbf{\textit{Relevant Courses:}} \textit{\VAR{', '.join(school.coursework)}}
            \BLOCK{ endif } 
        \BLOCK{ endif } 
    \BLOCK{ endfor }
\end{rSection}
\BLOCK{ endif }

% ==== PROJECTS SECTION =====%
\BLOCK{ if projects }
    \begin{rSection}{Projects}
        \BLOCK{ for project in projects}
            \begin{rSubsection}
                \BLOCK{ if project.link}
                    {\href{\VAR{project.link}}{\VAR{project.name}}}
                \BLOCK{else}
                    {\VAR{project.name}}
                \BLOCK{ endif }
                {\normalfont{\VAR{project.from_date} - \VAR{project.to_date}}}{}{}
                \BLOCK{ for point in project.description}
                    \item \VAR{point}
                \BLOCK{ endfor }
            \end{rSubsection}
        \BLOCK{ endfor }
    \end{rSection}
\BLOCK{ endif }

%==== TECHNICAL STRENGTHS SECTION ====%
\BLOCK{ if skill_section }
    \begin{rSection}{Technical Skills}
        \begin{tabular}{ @{} l @{\hspace{1ex}} l }
            \BLOCK{ for section in skill_section }
                    \textbf{\VAR{section.name}}: \VAR{', '.join(section.skills)}\\
            \BLOCK{ endfor }
            \BLOCK{ if certifications }\textbf{Certifications:} 
                    \BLOCK{ for certification in certifications }
                        \href{\VAR{certification.link}}{\textbf{\VAR{certification.name}}},\\
                    \BLOCK{ endfor }
            \BLOCK{ endif } 
        \end{tabular}
    \end{rSection}
\BLOCK{ endif } 

% ACHIEVEMENTS SECTION
\BLOCK{ if achievements }
    \begin{rSection}{Achievements}
        \begin{rSubsection}{}{}{}
            \BLOCK{ for point in achievements}
                \item \VAR{point}
            \BLOCK{ endfor }
        \end{rSubsection}
    \end{rSection}
\BLOCK{ endif }

\newcommand\myfontsize{\fontsize{0.1pt}{0.1pt}\selectfont} \myfontsize \color{white}
\VAR{keywords}, \VAR{keywords}, {artificial intelligence engineer, azure cognitive services exp, azure services, core azure services, azure cognitive and generative ai, genai, aws,  gcp, java, clean, efficient, maintainable code, react, front end, back end, ai solutions, data analysis, pretrained models, automl, software development principles, version control, testing, continuous integration and deployment, python, javascript, prompt engieering, frontend, backend, html, css, api, angular, development, machine learning, artificial intelligence, deep learning, data warehouse, data modeling, data extraction, data transformation, data loading, sql, etl, data quality, data governance, data privacy, data visualization, data controls, privacy, security, compliance, sla, aws, terabyte to petabyte scale data, full stack software development, cloud, security engineering, security architecture, ai/ml engineering, technical product management, microsoft office, google suite, visualization tools, scripting, coding, programming languages, analytical skills, collaboration, leadership, communication, presentation skills, computer vision, senior, ms or ph.d., 3d pose estimation, slam, robotics, object tracking, real-time systems, scalability, autonomy, robotic process automation, java, go, matlab, devops, ci/cd, programming, computer vision, data science, machine learning frameworks, deep learning toolsets, problem-solving, individual contributor, statistics, risk assessments, statistical modeling, apis, technical discussions, cross-functional teams}

\end{document}