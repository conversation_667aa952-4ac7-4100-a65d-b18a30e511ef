import asyncio
import j<PERSON>
from typing import Any, Dict, List, Optional, Union

import httpx
from supabase import create_client, Client

from common.infrastructure.supabase.config import supabase_settings


class SupabaseClient:
    """Client for Supabase operations.
    
    This singleton class provides methods to interact with Supabase tables.
    """
    _instance = None
    _client: Optional[Client] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SupabaseClient, cls).__new__(cls)
            cls._client = create_client(
                supabase_settings.SUPABASE_URL,
                supabase_settings.SUPABASE_KEY
            )
        return cls._instance
    
    @property
    def client(self) -> Client:
        """Get the Supabase client instance."""
        return self._client
    
    async def insert_record(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Insert a record into a Supabase table.
        
        Args:
            table_name: The name of the table to insert into
            data: Dictionary containing the data to insert
            
        Returns:
            The created record as returned by Supabase
        """
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, 
            lambda: self.client.table(table_name).insert(data).execute()
        )
        
        # Check if the response has data and return it
        if hasattr(result, 'data') and result.data:
            return result.data[0]  # Return the first item in data
        return {}
    
    async def update_record(self, table_name: str, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a record in a Supabase table.
        
        Args:
            table_name: The name of the table to update
            record_id: The ID of the record to update
            data: Dictionary containing the data to update
            
        Returns:
            The updated record as returned by Supabase
        """
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, 
            lambda: self.client.table(table_name).update(data).eq('id', record_id).execute()
        )
        
        # Check if the response has data and return it
        if hasattr(result, 'data') and result.data:
            return result.data[0]  # Return the first item in data
        return {}
    
    async def get_records(
        self, 
        table_name: str, 
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get records from a Supabase table with optional filters.
        
        Args:
            table_name: The name of the table to query
            filters: Dictionary of column-value pairs to filter by
            limit: Maximum number of records to return
            
        Returns:
            List of records that match the filters
        """
        loop = asyncio.get_event_loop()
        
        # Start building the query
        query = self.client.table(table_name).select('*')
        
        # Apply any filters
        if filters:
            for column, value in filters.items():
                query = query.eq(column, value)
        
        # Apply limit if specified
        if limit:
            query = query.limit(limit)
        
        # Execute the query
        result = await loop.run_in_executor(None, query.execute)
        
        # Check if the response has data and return it
        if hasattr(result, 'data'):
            return result.data
        return []
    
    async def get_record_by_id(self, table_name: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Get a record by its ID.
        
        Args:
            table_name: The name of the table to query
            record_id: The ID of the record to retrieve
            
        Returns:
            The record if found, None otherwise
        """
        records = await self.get_records(table_name, {'id': record_id}, 1)
        return records[0] if records else None


supabase_client = SupabaseClient()
