"""
Main API entry point for the Career Coach Agents platform.

This module provides a unified API that combines:
1. Career Coach API
2. Resume Editor API 
3. Job Materials Generator API

All APIs are mounted as sub-applications with their own routes.
"""

import os
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.routing import APIRoute
from typing import Dict, List

# Import the various API modules
from .career_coaches.infrastructure.api import app as career_coach_app
from .resume_editor.interfaces.api import app as resume_editor_app
from .material_generator.infrastructure.api import app as material_generator_app
from .job_scrapper.infrastructure.api import create_app as create_job_scrapper_app



# Create main FastAPI application
app = FastAPI(
    title="Career Coach Agents Platform API",
    description="Combined API for career coaching, resume editing, and job materials generation",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint for the main API
@app.get("/health")
async def health_check():
    """Health check endpoint for the main API."""
    return {
        "status": "healthy",
        "service": "career_coach_agents_platform",
        "version": "1.0.0"
    }


# List all available routes and sub-applications
@app.get("/routes")
async def list_routes(request: Request):
    """
    Lists all available routes and sub-applications in the API.
    This helps developers and consumers discover the API's capabilities.
    """
    
    # Get main app routes
    main_routes = []
    for route in app.routes:
        if isinstance(route, APIRoute):
            main_routes.append({
                "path": route.path,
                "name": route.name,
                "methods": list(route.methods),
                "description": route.description or ""
            })
    
    # Define mounted sub-applications with their prefixes
    sub_apps = [
        {
            "prefix": "/career-coach",
            "name": "Career Coach API",
            "description": "API for career coaching services"
        },
        {
            "prefix": "/resume-editor",
            "name": "Resume Editor API",
            "description": "API for resume editing services"
        },
        {
            "prefix": "/material-generator",
            "name": "Material Generator API",
            "description": "API for generating job application materials"
        },
        {
            "prefix": "/job-scraper",
            "name": "Job Scraper API",
            "description": "API for scraping job listings"
        }
    ]
    
    return {
        "main_api": {
            "title": app.title,
            "description": app.description,
            "version": app.version,
            "routes": main_routes
        },
        "sub_applications": sub_apps
    }


# Mount all sub-applications
app.mount("/career-coach", career_coach_app)
app.mount("/resume-editor", resume_editor_app)
app.mount("/material-generator", material_generator_app)
app.mount("/job-scraper", create_job_scrapper_app())


# Run the application
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment variable or use default
    port = int(os.environ.get("API_PORT", 8000))
    
    print(f"Starting Career Coach Agents Platform API on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
