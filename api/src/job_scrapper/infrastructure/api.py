from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from ..application.services import ScraperService
from ..application.dtos import ScrapeRequestD<PERSON>, JobPostingDTO
from .scrapper import <PERSON>ly<PERSON><PERSON><PERSON><PERSON>raper
from .logger import setup_logger
import logging

logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    app = FastAPI(title="Job Scraper API")
    setup_logger()
    logger = logging.getLogger(__name__)
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # For development, allow all origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app.get("/health")
    async def health_check():
        """Health check endpoint to verify the API is running properly."""
        return {
            "status": "healthy",
            "service": "job_scraper",
            "version": "1.0.0"
        }
    
    # Dependency Injection
    scraper = TavilyJobScraper()
    scraper_service = ScraperService(scraper=scraper)

    @app.post("/scrape", response_model=JobPostingDTO)
    async def scrape_job(request: ScrapeRequestDTO):
        try:
            result = await scraper_service.scrape_job_posting(request)
            if not result:
                raise HTTPException(status_code=404, detail="No data extracted from the provided URL")
            
            # Add detailed logging before returning result
            logger.info(f"Successful scrape for URL: {request.url}")
            logger.info(f"Job description type: {type(result.job_description)}, Company description type: {type(result.company_description)}")
            
            # Final validation check - ensure we have string values
            if result.job_description and not isinstance(result.job_description, str):
                logger.warning(f"Converting job_description from {type(result.job_description)} to string")
                result.job_description = str(result.job_description)
                
            if result.company_description and not isinstance(result.company_description, str):
                logger.warning(f"Converting company_description from {type(result.company_description)} to string")
                result.company_description = str(result.company_description)
                
            return result
            
        except ValueError as ve:
            logger.error(f"Invalid URL error: {str(ve)}")
            raise HTTPException(status_code=400, detail=str(ve))
        except Exception as e:
            logger.error(f"API error: {str(e)}")
            # Include more details in the error response for easier debugging
            error_detail = f"Error processing request: {str(e)}"
            raise HTTPException(status_code=500, detail=error_detail)

    return app