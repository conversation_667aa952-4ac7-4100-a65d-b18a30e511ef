from typing import Optional, Dict, Any
from pydantic import ValidationError
from ..domain.services import <PERSON>raperPort
from ..domain.value_objects import URL
from ..domain.entities import JobPosting
from .dtos import JobPostingDTO, ScrapeRequestDTO
import logging
import json

logger = logging.getLogger(__name__)

class ScraperService:
    def __init__(self, scraper: ScraperPort):
        self.scraper = scraper

    async def scrape_job_posting(self, request: ScrapeRequestDTO) -> Optional[JobPostingDTO]:
        try:
            url = URL(request.url)
            result = await self.scraper.scrape(url)
            
            if not result:
                logger.warning(f"No data extracted for URL: {request.url}")
                return None
                
            # Log the types and contents for debugging
            job_desc = result.job_description.content if result.job_description else None
            company_desc = result.company_description.content if result.company_description else None
            
            logger.info(f"Job description type: {type(job_desc)}, length: {len(str(job_desc)) if job_desc else 0}")
            logger.info(f"Company description type: {type(company_desc)}, length: {len(str(company_desc)) if company_desc else 0}")
            
            try:
                # Try to create the DTO with our data
                return JobPostingDTO(
                    job_description=job_desc,
                    company_description=company_desc,
                    company_name=result.company_name
                )
            except ValidationError as ve:
                # Log detailed validation error information
                logger.error(f"Validation error for URL {request.url}: {str(ve)}")
                logger.error(f"Job description preview: {str(job_desc)[:200]}...")
                logger.error(f"Company description preview: {str(company_desc)[:200]}...")
                
                # Try to fix the data if it's in JSON format
                if isinstance(job_desc, dict):
                    job_desc = json.dumps(job_desc)
                if isinstance(company_desc, dict):
                    company_desc = json.dumps(company_desc)
                
                # Try again with potentially fixed data
                return JobPostingDTO(
                    job_description=job_desc,
                    company_description=company_desc,
                    company_name=result.company_name
                )
                
        except ValueError as ve:
            logger.error(f"Invalid URL: {str(ve)}")
            raise
        except ValidationError as ve:
            logger.error(f"Validation error for URL {request.url}: {str(ve)}")
            raise
        except Exception as e:
            logger.error(f"Error scraping URL {request.url}: {str(e)}")
            raise