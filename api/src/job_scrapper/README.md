# Job Scrapper API

## Overview
The Job Scrapper API provides functionality to extract structured information from job posting URLs. It uses the Tavily search API to retrieve web content and a language model to extract meaningful job and company descriptions.

## Architecture
This module follows a clean architecture pattern:

- **Domain Layer**: Core business entities, value objects, and service interfaces
- **Application Layer**: Use cases, services, and DTOs
- **Infrastructure Layer**: External implementation details, API controllers, and adapters

## Setup

### Environment Variables
Create or update your `.env` file with the following variables:
```
GROQ_API_KEY=your_groq_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
```

### Dependencies
The module requires the following key dependencies:
- FastAPI
- tavily-python
- langchain-groq

These are already included in the project's `pyproject.toml`.

## Usage

### API Endpoints

#### 1. Health Check
```
GET /job-scraper/health
```
Returns the service status and version information.

#### 2. Scrape Job Posting
```
POST /job-scraper/scrape
```

Request Body:
```json
{
  "url": "https://example.com/job-posting"
}
```

Response:
```json
{
  "job_description": "Full job description extracted from the URL...",
  "company_description": "Company information extracted from the URL..."
}
```

## How It Works

1. When a job URL is submitted, the API uses Tavily's extract method to directly extract the webpage content
2. The extracted content is processed using a Groq LLM with a specialized prompt to extract structured information
3. The LLM returns job and company descriptions in a structured JSON format
4. The API returns the structured data to the client

## Error Handling
The API implements robust error handling for common scenarios:
- Invalid URLs
- Inaccessible web content
- LLM response parsing issues
- General server errors
